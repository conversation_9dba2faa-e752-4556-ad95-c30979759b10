import { useEffect, useRef, useState } from "react";
import { ChevronLeft, ChevronRight } from "lucide-react";
import { Button } from "@/components/ui/button";
import { motion } from "framer-motion";
import Image from "next/image";

// Gallery images - using stock photos for college graduation/convocation
const galleryImages = [
  // College graduation ceremony (6 images)
  { url: "https://images.unsplash.com/photo-1523050854058-8df90110c9d1?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&h=600", alt: "Graduation ceremony with students in caps and gowns" },
  { url: "https://images.unsplash.com/photo-1541339907198-e08756dedf3f?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&h=600", alt: "Graduates throwing caps in celebration" },
  { url: "https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&h=600", alt: "Student receiving diploma on stage" },
  { url: "https://images.unsplash.com/photo-1622495704404-436d3dc7cb6a?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&h=600", alt: "Graduates celebrating with families" },
  { url: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&h=600", alt: "Graduation stage and podium setup" },
  { url: "https://images.unsplash.com/photo-1523580494863-6f3031224c94?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&h=600", alt: "Academic procession with faculty" },
  
  // Academic convocation events (4 images)
  { url: "https://images.unsplash.com/photo-1562774053-701939374585?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&h=600", alt: "University campus architecture" },
  { url: "https://images.unsplash.com/photo-1576267423445-b2e0074d68a4?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&h=600", alt: "Academic convocation hall" },
  { url: "https://images.unsplash.com/photo-1513475382585-d06e58bcb0e0?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&h=600", alt: "Medal ceremony with officials" },
  { url: "https://images.unsplash.com/photo-1577896851231-70ef18881754?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&h=600", alt: "Group photo of graduates" },
  
  // University campus scenes (3 images)
  { url: "https://images.unsplash.com/photo-1541829070764-84a7d30dd3f3?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&h=600", alt: "University campus courtyard" },
  { url: "https://images.unsplash.com/photo-1498243691581-b145c3f54a5a?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&h=600", alt: "Campus library interior" },
  { url: "https://images.unsplash.com/photo-1507146153580-69a1fe6d8aa1?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&h=600", alt: "University campus at evening" }
];

export default function Gallery3D() {
  const containerRef = useRef<HTMLDivElement>(null);
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // Simulate loading delay for images
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 1000);

    return () => clearTimeout(timer);
  }, []);

  const nextImage = () => {
    setCurrentIndex((prev) => (prev + 1) % galleryImages.length);
  };

  const prevImage = () => {
    setCurrentIndex((prev) => (prev - 1 + galleryImages.length) % galleryImages.length);
  };

  const goToImage = (index: number) => {
    setCurrentIndex(index);
  };

  // Calculate rotation for 3D effect
  const getImageStyle = (index: number) => {
    const totalImages = galleryImages.length;
    const angleStep = 360 / totalImages;
    const currentAngle = -currentIndex * angleStep;
    const imageAngle = index * angleStep + currentAngle;
    
    return {
      transform: `
        rotateY(${imageAngle}deg) 
        translateZ(300px) 
        ${index === currentIndex ? 'scale(1.1)' : 'scale(0.9)'}
      `,
      opacity: Math.abs(imageAngle) > 90 ? 0.3 : 1,
      zIndex: index === currentIndex ? 10 : 1,
    };
  };

  if (isLoading) {
    return (
      <section id="gallery" className="py-24 relative overflow-hidden">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-playfair font-bold text-white mb-6">
              Event Photo Gallery
            </h2>
            <p className="text-xl text-gray-300 max-w-2xl mx-auto">
              Capturing the memorable moments of our graduation ceremony
            </p>
          </div>
          <div className="h-96 md:h-[500px] flex items-center justify-center">
            <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-green-500"></div>
          </div>
        </div>
      </section>
    );
  }

  return (
    <section id="gallery" className="py-24 relative overflow-hidden">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        
        {/* Section Header */}
        <motion.div 
          className="text-center mb-16"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
        >
          <h2 className="text-4xl md:text-5xl font-playfair font-bold text-white mb-6">
            Event Photo Gallery
          </h2>
          <p className="text-xl text-gray-300 max-w-2xl mx-auto">
            Capturing the memorable moments of our graduation ceremony
          </p>
        </motion.div>

        {/* 3D Gallery Container */}
        <div className="relative">
          <div 
            ref={containerRef}
            className="h-96 md:h-[500px] relative"
            style={{ perspective: '1000px' }}
          >
            <div 
              className="absolute inset-0 flex items-center justify-center"
              style={{ 
                transformStyle: 'preserve-3d',
                transition: 'transform 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94)'
              }}
            >
              {galleryImages.map((image, index) => (
                <motion.div
                  key={index}
                  className="absolute w-64 h-48 md:w-80 md:h-60 cursor-pointer"
                  style={getImageStyle(index)}
                  onClick={() => goToImage(index)}
                  whileHover={{ scale: index === currentIndex ? 1.15 : 0.95 }}
                  transition={{ duration: 0.3 }}
                >
                  <Image
                    src={image.url}
                    alt={image.alt}
                    width={320}
                    height={240}
                    className="w-full h-full object-cover rounded-2xl shadow-2xl"
                    loading="lazy"
                  />
                  {index === currentIndex && (
                    <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent rounded-2xl flex items-end justify-center p-4">
                      <p className="text-white text-sm text-center font-medium">
                        {image.alt}
                      </p>
                    </div>
                  )}
                </motion.div>
              ))}
            </div>
          </div>

          {/* Navigation Controls */}
          <div className="flex justify-center items-center mt-8 space-x-6">
            <Button
              onClick={prevImage}
              variant="outline"
              size="icon"
              className="w-14 h-14 bg-white/10 hover:bg-white/20 rounded-full border-white/20 text-white backdrop-blur-sm"
            >
              <ChevronLeft className="w-6 h-6" />
            </Button>
            
            <div className="flex space-x-2">
              {galleryImages.map((_, index) => (
                <button
                  key={index}
                  onClick={() => goToImage(index)}
                  className={`w-3 h-3 rounded-full transition-all ${
                    index === currentIndex ? 'bg-green-500' : 'bg-white/30'
                  }`}
                />
              ))}
            </div>
            
            <Button
              onClick={nextImage}
              variant="outline"
              size="icon"
              className="w-14 h-14 bg-white/10 hover:bg-white/20 rounded-full border-white/20 text-white backdrop-blur-sm"
            >
              <ChevronRight className="w-6 h-6" />
            </Button>
          </div>

          {/* Image Counter */}
          <div className="text-center mt-6">
            <span className="inline-block px-4 py-2 bg-white/10 rounded-full text-white backdrop-blur-sm">
              {currentIndex + 1} / {galleryImages.length}
            </span>
          </div>
        </div>
      </div>
    </section>
  );
}